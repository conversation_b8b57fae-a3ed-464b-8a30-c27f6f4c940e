const kStorageToken = 'token-storage';
const kStorageRefreshToken = 'refresh-token-storage';

const kStorageIsBiometricActive = 'biometric-status';
const kStorageUsername = 'username-encrypred';
const kStoragePassword = 'password-encrypted';
const kStorageNeedChangePass = 'need-change-password';

const kStorageUserId = 'user-id';
const kStorageAgentCode = 'agent-code';
const kStorageAgentName = 'agent-name';
const kStorageAgentBranch = 'agent-branch';
const kStorageUserFirestoreId = 'user-firestore-id';
const kStorageUserLevel = "user-level";
const kStorageUserUsername = "user-username";
const kStorageUserLevelName = "user-level-name";
const kStorageUserLevelComplete = "user-level-complete";
const kStorageUserChannel = "user-channel";
const kStorageUserRoles = "user-roles";
const kStorageUserType = "user-type";
const kStorageAreaCode = "area-code";
const kStorageMBranchCode = "mbranch-code";

const kDeviceId = 'device-id';
const kWebDeviceId = 'web-device-id';

// Network Dialog State
const kStorageNetworkDialogShown = 'network-dialog-shown-session';

// approval status can edit
const kApprovalStatusApprove = 'DISETUJUI';
const kApprovalStatusReject = 'DITOLAK';
const kApprovalStatusCanceled = 'DIBATALKAN';
// approvl status cannot edit
const kApprovalStatusNew = 'BARU';
const kApprovalStatusPending = 'TERTUNDA';
const kApprovalStatusWaiting = 'MENUNGGU_PERSETUJUAN';

// list of field
const kFieldleaderCode = 'leaderCode';
const kFieldagentCode = 'agentCode';
const kFieldagentName = 'agentName';
const kFielddistributionCode = 'distributionCode';
const kFieldroleName = 'roleName';
const kFieldlevel = 'level';
const kFieldpositionLevel = 'positionLevel';
const kFieldregionCode = 'regionCode';
const kFieldregionName = 'regionName';
const kFieldsubRegionCode = 'subRegionCode';
const kFieldsubRegionName = 'subRegionName';
const kFieldareaCode = 'areaCode';
const kFieldareaName = 'areaName';
const kFieldbranchCode = 'branchCode';
const kFieldbranchName = 'branchName';
const kFieldgroupCode = 'groupCode';
const kFieldgroupName = 'groupName';
const kFieldlicenseNumberAAJI = 'licenseNumberAAJI';
const kFieldlicenseExpiredDateAAJI = 'licenseExpiredDateAAJI';
const kFieldlicenseNumberAASI = 'licenseNumberAASI';
const kFieldlicenseExpiredDateAASI = 'licenseExpiredDateAASI';
const kFieldleaderG2G = 'leaderG2G';
const kFieldrecruiterCode = 'recruiterCode';
const kFielddob = 'dob';
const kFieldgender = 'gender';
const kFieldeducation = 'education';
const kFieldstatus = 'status';
const kFieldchannel = 'channel';
const kFieldemail = 'email';
const kFieldphoneNumber = 'phoneNumber';
const kFieldaddress = 'address';
const kFieldbankAccountNumber = 'bankAccountNumber';
const kFieldbank = 'bank';
const kFieldmaritalStatus = 'maritalStatus';
const kFieldbankAttachment = 'bankAttachment';
const kFieldktpAttachment = 'ktpAttachment';
const kFieldkkAttachment = 'kkAttachment';
const kFieldphoto = 'photo';
const kFieldcreatedAt = 'createdAt';
const kFieldupdatedAt = 'updatedAt';
const kFieldmbranchCode = 'mbranchCode';
const kFieldmbranchName = 'mbranchName';
const kFieldsbranchCode = 'sbranchCode';
const kFieldsbranchName = 'sbranchName';

const kInfoTypeTnc = 'PRIVACY_POLICY';
const kInfoTypeAboutUs = 'ABOUT_US';
const kInfoTypeMemo = 'MEMO';
const kInfoTypeFormCas = 'FORM_CAS';
const kInfoTypeFormOps = 'FORM_OPS';
const kInfoTypeSuratEdaran = 'SURAT_EDARAN';
const kInfoTypeNews = 'NEWS';
const kInfoTypeTutorial = 'TUTORIAL';
const kInfoTypeFingerPrint = 'FINGERPRINT';

// requestCode
const kReqLogin = 1;
const kReqAddDevices = 2;
const kReqGetDevices = 3;
const kReqRevokeDevices = 4;
const kReqUploadProfile = 5;
const kReqGetCurrentUpdateProfile = 6;
const kReqGetFlyer = 7;
const kReqGetInfo = 8;
const kReqGetProfile = 9;
// Widget Produksi Saya summary
const kReqGetWgtSumMonth = 10;
const kReqGetWgtSumYear = 11;
// Widget Produksi saya Page
const kReqWidgetProductionMonth = 12;
const kReqWidgetProductionYear = 13;
const kReqWidgetProductionTeamMonth = 20;
const kReqWidgetProductionTeamYear = 21;
const kReqGetPromosiAgent = 14;
const kReqGetValidasiHirarki = 15;
const kReqGetCommission = 16;
const kReqGetDetailCommission = 17;
const kReqGetPersistencyIndividu = 18;
const kReqGetPersistencyTeam = 19;
const kReqGetPersistencyArea = 22;
const kReqGetProductionGraphicMonth = 22;
const kReqGetProductionGraphicYear = 23;
// ComboBox
const kReqGetComboBoxLevel = 24;
const kReqGetComboBoxBank = 25;
const kReqGetBranch = 26;
const kReqGetAllBranch = 100;
const kReqGetBranchList = 101;
const kReqGetComboBoxOccupation = 27;
const kReqGetEducationCombo = 54;
const kReqGetBankCombo = 55;
const kReqGetMaritalStatusCombo = 56;
const kReqGetSimpleProfile = 57;
// Recruitment
const kReqSaveDraft = 28;
const kReqGetRecruitmentList = 29;
const kReqPostRequestVerif = 30;
const kReqGetIsVerified = 31;
const kReqSubmitRecruitmentForm = 32;
const kReqGetRecruitmentDetail = 44;

//INBOX
const kReqGetInbox = 24;
const kReqReadInbox = 25;
const kReqArchiveInbox = 26;
const kReqDeleteInbox = 27;
const kReqDeletePermanentInbox = 28;
const kReqDetailInbox = 29;
const kReqCountUnreadInbox = 30;

// config
const kReqConfGlobalPkaj = 33;
const kReqConfGlobalEtic = 34;
const kReqConfGlobalAntiTwist = 35;
const kReqConfGlobalPmkaj = 36;
const kReqPatchSignaturePublic = 37;
const kReqVerifyEmailPublic = 59;

//REJOIN
const kReqRejoinEligibleCandidates = 31;
const kReqRequestedRejoin = 32;
const kReqRequestCancelRejoin = 38;
const kReqRequestUploadDocRejoin = 39;
const kReqSubmitRejoin = 40;
const kReqDetailRejoin = 41;
const kReqListApprovalRejoin = 42;
const kReqPostApproval = 43;
const kReqListApprovalHistory = 44;

//TERMINASI
const kReqSubmitTermination = 44;
const kReqGetTerminationList = 45;
const kReqGetTerminationDetail = 451;
const kReqCancelTermination = 46;
const kReqGetEligibleCandidateTermination = 47;
const kReqSubmitPolicyTransfer = 48;
const kReqUpdateTargetPolicyTransfer = 49;
const kReqPolicyTransferList = 50;
const kReqAcceptPolicyTransfer = 51;
const kReqRejectPolicyTransfer = 52;
const kReqGetQuestionDetail = 53;

const kReqPerformLogout = 58;
const kReqGenerateContractPdf = 60;

const kReqSubmitInterview = 61;

// firestore
const kFireCollectionAgent = "agents";
const kFireDocTheme = "theme_is_dark";
const kFireDocLanguage = "language";

// UserLEvel
const kUserLevelBp = 'ROLE_AGE_BP';
const kUserLevelBm = 'ROLE_AGE_BM';
const kUserLevelBd = 'ROLE_AGE_BD';
const kUserLevelBdm = 'ROLE_AGE_BDM';
const kUserLevelBDD = 'ROLE_AGE_BDD';
const kUserLevelABDD = 'ROLE_AGE_ABDD';
const kUserLevelHOS = 'ROLE_AGE_HOS';
const kUserLevelCAO = 'ROLE_AGE_CAO';
const kUserLevelARA = 'ROLE_AGE_ARA';
const kUserLevelAgeBranch = 'ROLE_AGE_BRANCH';
const kUserLevelSecreatryBd = 'ROLE_AGE_SECRETARY_BD';
const kUserLevelSecreatryBm = 'ROLE_AGE_SECRETARY_BM';
const kUserLevelSecreatryBp = 'ROLE_AGE_SECRETARY_BP';

// UserLevelBan
const kUserLevelBanASM = 'ROLE_BAN_ASM'; //-> ASM
const kUserLevelBanSASM = 'ROLE_BAN_SASM'; //-> ASM
const kUserLevelBanBO = 'ROLE_BAN_BO'; //-> BO
const kUserLevelBanEBO = 'ROLE_BAN_EBO'; //-> BO
const kUserLevelBanSBO = 'ROLE_BAN_SBO'; //-> BO
const kUserLevelBanSEBO = 'ROLE_BAN_SEBO'; //-> BO
const kUserLevelBanRSM = 'ROLE_BAN_RAD_RSM';
const kUserLevelBanBS = 'ROLE_BAN_BS';
const kUserLevelBanHOB = 'ROLE_BAN_HOB';
const kUserLevelBanHOR = 'ROLE_BAN_HOR';
const kUserLevelBanHOS = 'ROLE_BAN_RAD_HOS';
const kUserLevelBanSR = 'ROLE_BAN_SR';
const kUserLevelBanBTR = 'ROLE_BAN_BTR';

const kUserChannelBan = 'BAN';
const kuserChannelAge = 'AGE';

// others
const kLevelBP = 'BP';
const kLevelBM = 'BM';
const kLevelBD = 'BD';
const kLevelBO = 'BO';
const kLevelASM = 'AM';

// switch yearly
const kSwitchYearly = 'select-year';
const kSwitchMonthly = 'select-month';
// Switch team
const kSwitchProdTeam = 'select-team';
const kSwitchProdIndividu = 'select-individu';
// Switch for other user levels
const kSwitchProdGroup = 'select-group';
const kSwitchProdBranch = 'select-branch';
const kSwitchProdArea = 'select-area';
const kSwitchProdRegion = 'select-region';
const kSwitchProdTerritory = 'select-territory';

// Routes Arguments
const kArgsUserLevel = 'user-level';
const kArgsProductionType = 'production-type';
const kArgsProductionTeamType = 'production-team-type';
const kArgsTerminationReason = 'termination-reason';
const kArgsSelfTermination = 'self-termination';
const kArgsTerminationData = 'termination-data';
const kArgsTerminationApprovalHeaderId = 'termination-approval-header-id';

// SPAJ Status
const kSpajStatus = [
  "pending_uw",
  "pending_admin",
  "pending_checker",
  "terbit_polis",
  "ditolak",
];
const kSpajStatPendAdmin = 'PENDING ADMIN';
const kSpajStatPendUw = 'PENDING UW';
const kSpajStatPendCheck = 'PENDING CHECKER';
const kSpajStatAccept = 'ACCEPT';
const kSpajStatAcceptPendingPrint = "Pending Policy Printing";
const kSpajStatReject = 'REJECT';
const kSpajStatPolicy = 'POLICY';

// Widget Keys
const kWidgetKeyUlangTahunNasabah = 'ulangTahunNasabah';
const kWidgetKeyStatusKlaim = 'statusKlaim';
const kWidgetKeyPolisLapse = 'polisLapse';
const kWidgetKeyPolisJatuhTempo = 'polisJatuhTempo';
const kWidgetKeyProduksiSaya = 'produksiSaya';
const kWidgetKeyValidasiPromosi = 'validasiPromosi';
const kWidgetKeyEstimasiKompensasi = 'estimasiKompensasi';
const kWidgetKeyPersistensi = 'persistensi';
const kWidgetKeyStatusSpaj = 'statusSpaj';

// Photo Type
const kPhotoTypeKtp = 'ktp';
const kPhotoTypeSelfieKtp = 'selfie-ktp';
const kPhotoTypePasFoto = 'pas-foto';
const kPhotoTypeSignature = 'signature';
const kPhotoTypeParaf = 'paraf';

// Config Keys

const kConfKeyPkaj = 'recruitment.document.pkaj';
const kConfKeyEtic = 'recruitment.document.kode_etik';
const kConfKeyAntiTwist = ' recruitment.document.anti_twisting';
const kConfKeyPmkaj = 'recruitment.document.pmkaj';

const kNoCachedDataMsg = 'Tidak ada data cache yang tersedia';
